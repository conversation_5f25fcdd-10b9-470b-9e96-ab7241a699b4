'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Bell, 
  Search, 
  User, 
  Settings, 
  LogOut, 
  FileText,
  Users,
  Zap,
  CreditCard,
  BarChart3,
  Plus,
  Download
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface HeaderProps {
  title?: string;
  className?: string;
}

export default function Header({ title, className }: HeaderProps) {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Navigation items for VeriAPI
  const navigationItems = [
    {
      name: 'Panel',
      href: '/dashboard',
      icon: BarChart3,
      current: pathname === '/dashboard'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      href: '/dashboard/invoices',
      icon: FileText,
      current: pathname.startsWith('/dashboard/invoices')
    },
    {
      name: 'Client<PERSON>',
      href: '/dashboard/customers',
      icon: Users,
      current: pathname.startsWith('/dashboard/customers')
    },
    {
      name: 'Integraciones',
      href: '/dashboard/integrations',
      icon: Zap,
      current: pathname.startsWith('/dashboard/integrations')
    },
    {
      name: 'Suscripción',
      href: '/dashboard/subscription',
      icon: CreditCard,
      current: pathname.startsWith('/dashboard/subscription')
    },
    {
      name: 'Configuración',
      href: '/dashboard/settings',
      icon: Settings,
      current: pathname.startsWith('/dashboard/settings')
    }
  ];

  const unreadCount = 2;

  return (
    <header className={cn('bg-slate-800 border-b border-slate-700', className)}>
      <div className="max-w-7xl mx-auto">
        <div className="flex h-16 items-center justify-between px-6 sm:px-8">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-8">
            {/* Logo/Brand */}
            <div className="flex items-center">
              <div className="text-xl font-bold text-white">
                VeriAPI
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex space-x-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.name}
                    onClick={() => router.push(item.href)}
                    className={cn(
                      'flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                      item.current
                        ? 'bg-slate-700 text-white'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700'
                    )}
                  >
                    <Icon className="h-4 w-4 mr-2" />
                    {item.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Right side - Search and Actions */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="hidden lg:block">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search across VeriAPI"
                  className="w-80 bg-slate-700 border-slate-600 text-white placeholder-slate-400 pl-10 pr-4 py-2 rounded-lg border focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              {/* Export Button */}
              <button className="hidden sm:flex items-center px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors text-sm">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>

              {/* Create Invoice Button */}
              <button 
                onClick={() => router.push('/dashboard/invoices/create')}
                className="flex items-center px-3 py-2 bg-emerald-600 hover:bg-emerald-700 text-white rounded-lg transition-colors text-sm"
              >
                <Plus className="h-4 w-4 mr-2" />
                Nueva Factura
              </button>
            </div>

            {/* Notifications */}
            <div className="relative">
              <button
                type="button"
                className="p-2 text-slate-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500 rounded-md transition-colors"
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              >
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <span className="absolute -top-0.5 -right-0.5 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {unreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Profile dropdown */}
            <div className="relative">
              <button
                type="button"
                className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              >
                <div className="h-8 w-8 rounded-full bg-emerald-600 flex items-center justify-center">
                  <span className="text-sm font-medium text-white">
                    {user?.firstName?.charAt(0) || 'U'}
                  </span>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <div className="md:hidden border-t border-slate-700">
        <div className="px-6 py-3 space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => router.push(item.href)}
                className={cn(
                  'flex items-center w-full px-3 py-2 rounded-lg text-sm font-medium transition-colors',
                  item.current
                    ? 'bg-slate-700 text-white'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700'
                )}
              >
                <Icon className="h-4 w-4 mr-3" />
                {item.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Close dropdowns when clicking outside */}
      {(isProfileMenuOpen || isNotificationsOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileMenuOpen(false);
            setIsNotificationsOpen(false);
          }}
        />
      )}
    </header>
  );
}
