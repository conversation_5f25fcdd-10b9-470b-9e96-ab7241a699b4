'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import {
  Bell,
  Search,
  User,
  Settings,
  LogOut,
  FileText,
  Users,
  Zap,
  CreditCard,
  BarChart3,
  Plus,
  Download,
  Sun,
  Moon
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

interface HeaderProps {
  title?: string;
  className?: string;
}

export default function Header({ title, className }: HeaderProps) {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const router = useRouter();
  const pathname = usePathname();

  // Navigation items for VeriAPI
  const navigationItems = [
    {
      name: 'Panel',
      href: '/dashboard',
      icon: BarChart3,
      current: pathname === '/dashboard'
    },
    {
      name: 'Facturas',
      href: '/dashboard/invoices',
      icon: FileText,
      current: pathname.startsWith('/dashboard/invoices')
    },
    {
      name: 'Clientes',
      href: '/dashboard/customers',
      icon: Users,
      current: pathname.startsWith('/dashboard/customers')
    },
    {
      name: 'Integraciones',
      href: '/dashboard/integrations',
      icon: Zap,
      current: pathname.startsWith('/dashboard/integrations')
    }
  ];

  // Profile dropdown items
  const profileMenuItems = [
    {
      name: 'Suscripción',
      href: '/dashboard/subscription',
      icon: CreditCard
    },
    {
      name: 'Ajustes de Perfil',
      href: '/dashboard/settings/profile',
      icon: User
    },
    {
      name: 'Ajustes de Empresa',
      href: '/dashboard/settings/company',
      icon: Settings
    }
  ];

  const unreadCount = 2;

  return (
    <header className={cn('bg-gradient-to-r from-white via-gray-50 to-white dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 border-b border-gray-200 dark:border-slate-700/50 backdrop-blur-sm shadow-lg transition-colors duration-300', className)}>
      <div className="max-w-7xl mx-auto">
        <div className="flex h-18 items-center justify-between px-6 sm:px-8">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-10">
            {/* Logo/Brand */}
            <div className="flex items-center group">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-lg blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                <div className="relative bg-gradient-to-r from-emerald-500 to-blue-500 bg-clip-text text-transparent text-2xl font-bold tracking-tight">
                  VeriAPI
                </div>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex space-x-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.name}
                    onClick={() => router.push(item.href)}
                    className={cn(
                      'group relative flex items-center px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105',
                      item.current
                        ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-gray-900 dark:text-white shadow-lg border border-emerald-500/30'
                        : 'text-gray-600 dark:text-slate-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-slate-700/50 hover:shadow-md'
                    )}
                  >
                    <Icon className={cn(
                      'h-4 w-4 mr-2 transition-colors duration-300',
                      item.current ? 'text-emerald-400' : 'text-gray-500 dark:text-slate-400 group-hover:text-emerald-400'
                    )} />
                    {item.name}
                    {item.current && (
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400 rounded-full"></div>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Right side - Theme Toggle and Profile */}
          <div className="flex items-center space-x-6">
            {/* Theme Toggle */}
            <div className="flex items-center">
              <button
                onClick={toggleTheme}
                className="p-2.5 text-gray-500 dark:text-slate-400 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50 rounded-xl transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-700/50 hover:scale-110"
                title={theme === 'dark' ? 'Switch to light mode' : 'Switch to dark mode'}
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" />
                ) : (
                  <Moon className="h-5 w-5" />
                )}
              </button>
            </div>

            {/* Notifications */}
            <div className="relative">
              <button
                type="button"
                className="relative p-2.5 text-gray-500 dark:text-slate-400 hover:text-gray-900 dark:hover:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50 rounded-xl transition-all duration-300 hover:bg-gray-100 dark:hover:bg-slate-700/50 hover:scale-110 backdrop-blur-sm"
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              >
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-lg animate-pulse">
                    {unreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Profile dropdown */}
            <div className="relative mt-1">
              <button
                type="button"
                className="group flex items-center space-x-3 px-4 py-2.5 bg-white/20 dark:bg-slate-700/20 hover:bg-white/40 dark:hover:bg-slate-700/40 border border-gray-300/20 dark:border-slate-600/20 hover:border-gray-400/30 dark:hover:border-slate-500/30 rounded-xl transition-all duration-300 hover:shadow-lg backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-emerald-500/30"
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              >
                {/* Avatar */}
                <div className="relative">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                  <div className="relative h-10 w-10 rounded-lg bg-gradient-to-r from-emerald-600 to-emerald-700 flex items-center justify-center shadow-lg">
                    <span className="text-sm font-medium text-white">
                      {user?.firstName?.charAt(0) || 'U'}
                    </span>
                  </div>
                </div>

                {/* User Info */}
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-bold text-gray-900 dark:text-white">
                    {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : user?.fullName || 'Usuario'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-slate-400">
                    {user?.email || '<EMAIL>'}
                  </div>
                </div>

                {/* Dropdown Arrow */}
                <div className="text-gray-500 dark:text-slate-400 group-hover:text-gray-900 dark:group-hover:text-white transition-colors duration-300">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>

              {/* Dropdown Menu */}
              {isProfileMenuOpen && (
                <div className="absolute right-0 mt-2 w-64 bg-white/95 dark:bg-slate-800/95 backdrop-blur-sm border border-gray-200 dark:border-slate-700/50 rounded-xl shadow-xl z-50">
                  <div className="py-2">
                    {profileMenuItems.map((item) => {
                      const Icon = item.icon;
                      return (
                        <button
                          key={item.name}
                          onClick={() => {
                            router.push(item.href);
                            setIsProfileMenuOpen(false);
                          }}
                          className="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-slate-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-slate-700/50 transition-all duration-200"
                        >
                          <Icon className="h-4 w-4 mr-3 text-gray-500 dark:text-slate-400" />
                          {item.name}
                        </button>
                      );
                    })}

                    {/* Theme Selector */}
                    <div className="border-t border-gray-200 dark:border-slate-700/50 my-2"></div>
                    <div className="px-4 py-2">
                      <div className="text-xs font-medium text-gray-500 dark:text-slate-400 mb-2">Tema</div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            if (theme !== 'light') toggleTheme();
                          }}
                          className={cn(
                            'flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200',
                            theme === 'light'
                              ? 'bg-emerald-100 text-emerald-700 border border-emerald-200'
                              : 'text-gray-600 dark:text-slate-400 hover:bg-gray-100 dark:hover:bg-slate-700/50'
                          )}
                        >
                          <Sun className="h-3 w-3 mr-1" />
                          Claro
                        </button>
                        <button
                          onClick={() => {
                            if (theme !== 'dark') toggleTheme();
                          }}
                          className={cn(
                            'flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200',
                            theme === 'dark'
                              ? 'bg-emerald-100 text-emerald-700 border border-emerald-200'
                              : 'text-gray-600 dark:text-slate-400 hover:bg-gray-100 dark:hover:bg-slate-700/50'
                          )}
                        >
                          <Moon className="h-3 w-3 mr-1" />
                          Oscuro
                        </button>
                      </div>
                    </div>

                    {/* Divider */}
                    <div className="border-t border-gray-200 dark:border-slate-700/50 my-2"></div>

                    {/* Logout */}
                    <button
                      onClick={() => {
                        logout();
                        setIsProfileMenuOpen(false);
                      }}
                      className="flex items-center w-full px-4 py-3 text-sm text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-500/10 transition-all duration-200"
                    >
                      <LogOut className="h-4 w-4 mr-3" />
                      Cerrar Sesión
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <div className="md:hidden border-t border-gray-200 dark:border-slate-700/50 bg-gradient-to-r from-white/50 to-gray-50/50 dark:from-slate-900/50 dark:to-slate-800/50 backdrop-blur-sm">
        <div className="px-6 py-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => router.push(item.href)}
                className={cn(
                  'flex items-center w-full px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300',
                  item.current
                    ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-gray-900 dark:text-white shadow-lg border border-emerald-500/30'
                    : 'text-gray-600 dark:text-slate-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-slate-700/50 hover:shadow-md'
                )}
              >
                <Icon className={cn(
                  'h-4 w-4 mr-3 transition-colors duration-300',
                  item.current ? 'text-emerald-400' : 'text-gray-500 dark:text-slate-400'
                )} />
                {item.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Close dropdowns when clicking outside */}
      {(isProfileMenuOpen || isNotificationsOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileMenuOpen(false);
            setIsNotificationsOpen(false);
          }}
        />
      )}
    </header>
  );
}
