'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Bell, 
  Search, 
  User, 
  Settings, 
  LogOut, 
  FileText,
  Users,
  Zap,
  CreditCard,
  BarChart3,
  Plus,
  Download
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface HeaderProps {
  title?: string;
  className?: string;
}

export default function Header({ title, className }: HeaderProps) {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Navigation items for VeriAPI
  const navigationItems = [
    {
      name: 'Panel',
      href: '/dashboard',
      icon: BarChart3,
      current: pathname === '/dashboard'
    },
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      href: '/dashboard/invoices',
      icon: FileText,
      current: pathname.startsWith('/dashboard/invoices')
    },
    {
      name: 'Client<PERSON>',
      href: '/dashboard/customers',
      icon: Users,
      current: pathname.startsWith('/dashboard/customers')
    },
    {
      name: 'Integraciones',
      href: '/dashboard/integrations',
      icon: Zap,
      current: pathname.startsWith('/dashboard/integrations')
    },
    {
      name: 'Suscripción',
      href: '/dashboard/subscription',
      icon: CreditCard,
      current: pathname.startsWith('/dashboard/subscription')
    },
    {
      name: 'Configuración',
      href: '/dashboard/settings',
      icon: Settings,
      current: pathname.startsWith('/dashboard/settings')
    }
  ];

  const unreadCount = 2;

  return (
    <header className={cn('bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-slate-700/50 backdrop-blur-sm shadow-lg', className)}>
      <div className="max-w-7xl mx-auto">
        <div className="flex h-18 items-center justify-between px-6 sm:px-8">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-10">
            {/* Logo/Brand */}
            <div className="flex items-center group">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-lg blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                <div className="relative bg-gradient-to-r from-emerald-500 to-blue-500 bg-clip-text text-transparent text-2xl font-bold tracking-tight">
                  VeriAPI
                </div>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex space-x-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.name}
                    onClick={() => router.push(item.href)}
                    className={cn(
                      'group relative flex items-center px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105',
                      item.current
                        ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-white shadow-lg border border-emerald-500/30'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-md'
                    )}
                  >
                    <Icon className={cn(
                      'h-4 w-4 mr-2 transition-colors duration-300',
                      item.current ? 'text-emerald-400' : 'text-slate-400 group-hover:text-emerald-400'
                    )} />
                    {item.name}
                    {item.current && (
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400 rounded-full"></div>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Right side - Search and Actions */}
          <div className="flex items-center space-x-5">
            {/* Search */}
            <div className="hidden lg:block">
              <div className="relative group">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 group-focus-within:text-emerald-400 w-4 h-4 transition-colors duration-300" />
                <input
                  type="text"
                  placeholder="Search across VeriAPI"
                  className="w-80 bg-slate-700/50 backdrop-blur-sm border border-slate-600/50 text-white placeholder-slate-400 pl-10 pr-4 py-2.5 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 focus:bg-slate-700/70 transition-all duration-300 hover:bg-slate-700/60"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              {/* Export Button */}
              <button className="hidden sm:flex items-center px-4 py-2.5 bg-slate-700/50 hover:bg-slate-600/60 text-white rounded-xl transition-all duration-300 text-sm font-medium hover:scale-105 hover:shadow-lg backdrop-blur-sm border border-slate-600/30 hover:border-slate-500/50">
                <Download className="h-4 w-4 mr-2" />
                Exportar
              </button>

              {/* Create Invoice Button */}
              <button
                onClick={() => router.push('/dashboard/invoices/create')}
                className="relative flex items-center px-4 py-2.5 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-500 hover:to-emerald-600 text-white rounded-xl transition-all duration-300 text-sm font-medium hover:scale-105 hover:shadow-lg shadow-emerald-500/25 hover:shadow-emerald-500/40"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-emerald-400 to-emerald-500 rounded-xl opacity-0 hover:opacity-20 transition-opacity duration-300"></div>
                <Plus className="h-4 w-4 mr-2 relative z-10" />
                <span className="relative z-10">Nueva Factura</span>
              </button>
            </div>

            {/* Notifications */}
            <div className="relative">
              <button
                type="button"
                className="relative p-2.5 text-slate-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50 rounded-xl transition-all duration-300 hover:bg-slate-700/50 hover:scale-110 backdrop-blur-sm"
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              >
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-lg animate-pulse">
                    {unreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Profile dropdown */}
            <div className="relative">
              <button
                type="button"
                className="group flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:ring-offset-2 focus:ring-offset-slate-800 transition-all duration-300 hover:scale-110"
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              >
                <div className="relative">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                  <div className="relative h-9 w-9 rounded-full bg-gradient-to-r from-emerald-600 to-emerald-700 flex items-center justify-center shadow-lg">
                    <span className="text-sm font-medium text-white">
                      {user?.firstName?.charAt(0) || 'U'}
                    </span>
                  </div>
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <div className="md:hidden border-t border-slate-700/50 bg-gradient-to-r from-slate-900/50 to-slate-800/50 backdrop-blur-sm">
        <div className="px-6 py-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => router.push(item.href)}
                className={cn(
                  'flex items-center w-full px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300',
                  item.current
                    ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-white shadow-lg border border-emerald-500/30'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-md'
                )}
              >
                <Icon className={cn(
                  'h-4 w-4 mr-3 transition-colors duration-300',
                  item.current ? 'text-emerald-400' : 'text-slate-400'
                )} />
                {item.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Close dropdowns when clicking outside */}
      {(isProfileMenuOpen || isNotificationsOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileMenuOpen(false);
            setIsNotificationsOpen(false);
          }}
        />
      )}
    </header>
  );
}
