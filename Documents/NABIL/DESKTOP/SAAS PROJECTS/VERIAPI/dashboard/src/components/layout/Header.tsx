'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { 
  Bell, 
  Search, 
  User, 
  Settings, 
  LogOut, 
  FileText,
  Users,
  Zap,
  CreditCard,
  BarChart3,
  Plus,
  Download
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { cn } from '@/lib/utils';

interface HeaderProps {
  title?: string;
  className?: string;
}

export default function Header({ title, className }: HeaderProps) {
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  // Navigation items for VeriAPI
  const navigationItems = [
    {
      name: 'Panel',
      href: '/dashboard',
      icon: BarChart3,
      current: pathname === '/dashboard'
    },
    {
      name: '<PERSON>act<PERSON><PERSON>',
      href: '/dashboard/invoices',
      icon: FileText,
      current: pathname.startsWith('/dashboard/invoices')
    },
    {
      name: 'Client<PERSON>',
      href: '/dashboard/customers',
      icon: Users,
      current: pathname.startsWith('/dashboard/customers')
    },
    {
      name: 'Integraciones',
      href: '/dashboard/integrations',
      icon: Zap,
      current: pathname.startsWith('/dashboard/integrations')
    }
  ];

  // Profile dropdown items
  const profileMenuItems = [
    {
      name: 'Suscripción',
      href: '/dashboard/subscription',
      icon: CreditCard
    },
    {
      name: 'Ajustes de Perfil',
      href: '/dashboard/settings/profile',
      icon: User
    },
    {
      name: 'Ajustes de Empresa',
      href: '/dashboard/settings/company',
      icon: Settings
    }
  ];

  const unreadCount = 2;

  return (
    <header className={cn('bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-slate-700/50 backdrop-blur-sm shadow-lg', className)}>
      <div className="max-w-7xl mx-auto">
        <div className="flex h-18 items-center justify-between px-6 sm:px-8">
          {/* Left side - Logo and Navigation */}
          <div className="flex items-center space-x-10">
            {/* Logo/Brand */}
            <div className="flex items-center group">
              <div className="relative">
                <div className="absolute -inset-1 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-lg blur opacity-25 group-hover:opacity-40 transition duration-300"></div>
                <div className="relative bg-gradient-to-r from-emerald-500 to-blue-500 bg-clip-text text-transparent text-2xl font-bold tracking-tight">
                  VeriAPI
                </div>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex space-x-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                return (
                  <button
                    key={item.name}
                    onClick={() => router.push(item.href)}
                    className={cn(
                      'group relative flex items-center px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 hover:scale-105',
                      item.current
                        ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-white shadow-lg border border-emerald-500/30'
                        : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-md'
                    )}
                  >
                    <Icon className={cn(
                      'h-4 w-4 mr-2 transition-colors duration-300',
                      item.current ? 'text-emerald-400' : 'text-slate-400 group-hover:text-emerald-400'
                    )} />
                    {item.name}
                    {item.current && (
                      <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-gradient-to-r from-emerald-400 to-blue-400 rounded-full"></div>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Right side - Search and Profile */}
          <div className="flex items-center space-x-6">
            {/* Search */}
            <div className="hidden lg:block">
              <div className="relative group">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 group-focus-within:text-emerald-400 w-4 h-4 transition-colors duration-300" />
                <input
                  type="text"
                  placeholder="Search across VeriAPI"
                  className="w-80 bg-slate-700/50 backdrop-blur-sm border border-slate-600/50 text-white placeholder-slate-400 pl-10 pr-4 py-2.5 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 focus:bg-slate-700/70 transition-all duration-300 hover:bg-slate-700/60"
                />
              </div>
            </div>

            {/* Notifications */}
            <div className="relative">
              <button
                type="button"
                className="relative p-2.5 text-slate-400 hover:text-white focus:outline-none focus:ring-2 focus:ring-emerald-500/50 rounded-xl transition-all duration-300 hover:bg-slate-700/50 hover:scale-110 backdrop-blur-sm"
                onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
              >
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-lg animate-pulse">
                    {unreadCount}
                  </span>
                )}
              </button>
            </div>

            {/* Profile dropdown */}
            <div className="relative">
              <button
                type="button"
                className="group flex items-center space-x-3 px-4 py-2.5 bg-slate-700/30 hover:bg-slate-700/50 border border-slate-600/50 hover:border-slate-500/70 rounded-xl transition-all duration-300 hover:shadow-lg backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-emerald-500/50"
                onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
              >
                {/* Avatar */}
                <div className="relative">
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                  <div className="relative h-10 w-10 rounded-lg bg-gradient-to-r from-emerald-600 to-emerald-700 flex items-center justify-center shadow-lg">
                    <span className="text-sm font-medium text-white">
                      {user?.firstName?.charAt(0) || 'U'}
                    </span>
                  </div>
                </div>

                {/* User Info */}
                <div className="hidden sm:block text-left">
                  <div className="text-sm font-bold text-white">
                    {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : user?.fullName || 'Usuario'}
                  </div>
                  <div className="text-xs text-slate-400">
                    {user?.email || '<EMAIL>'}
                  </div>
                </div>

                {/* Dropdown Arrow */}
                <div className="text-slate-400 group-hover:text-white transition-colors duration-300">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </button>

              {/* Dropdown Menu */}
              {isProfileMenuOpen && (
                <div className="absolute right-0 mt-2 w-64 bg-slate-800/95 backdrop-blur-sm border border-slate-700/50 rounded-xl shadow-xl z-50">
                  <div className="py-2">
                    {profileMenuItems.map((item) => {
                      const Icon = item.icon;
                      return (
                        <button
                          key={item.name}
                          onClick={() => {
                            router.push(item.href);
                            setIsProfileMenuOpen(false);
                          }}
                          className="flex items-center w-full px-4 py-3 text-sm text-slate-300 hover:text-white hover:bg-slate-700/50 transition-all duration-200"
                        >
                          <Icon className="h-4 w-4 mr-3 text-slate-400" />
                          {item.name}
                        </button>
                      );
                    })}

                    {/* Divider */}
                    <div className="border-t border-slate-700/50 my-2"></div>

                    {/* Logout */}
                    <button
                      onClick={() => {
                        logout();
                        setIsProfileMenuOpen(false);
                      }}
                      className="flex items-center w-full px-4 py-3 text-sm text-red-400 hover:text-red-300 hover:bg-red-500/10 transition-all duration-200"
                    >
                      <LogOut className="h-4 w-4 mr-3" />
                      Cerrar Sesión
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <div className="md:hidden border-t border-slate-700/50 bg-gradient-to-r from-slate-900/50 to-slate-800/50 backdrop-blur-sm">
        <div className="px-6 py-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => router.push(item.href)}
                className={cn(
                  'flex items-center w-full px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300',
                  item.current
                    ? 'bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-white shadow-lg border border-emerald-500/30'
                    : 'text-slate-300 hover:text-white hover:bg-slate-700/50 hover:shadow-md'
                )}
              >
                <Icon className={cn(
                  'h-4 w-4 mr-3 transition-colors duration-300',
                  item.current ? 'text-emerald-400' : 'text-slate-400'
                )} />
                {item.name}
              </button>
            );
          })}
        </div>
      </div>

      {/* Close dropdowns when clicking outside */}
      {(isProfileMenuOpen || isNotificationsOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileMenuOpen(false);
            setIsNotificationsOpen(false);
          }}
        />
      )}
    </header>
  );
}
