'use client';

import { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import {
  User,
  Save,
  Lock,
  Mail,
  Phone,
  Calendar,
  Shield,
  Bell,
  Sun,
  Moon
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useTheme } from '@/contexts/ThemeContext';
import { cn } from '@/lib/utils';

export default function ProfileSettingsPage() {
  const { user } = useAuth();
  const { theme, setTheme } = useTheme();
  const [isLoading, setIsLoading] = useState(false);
  const [profile, setProfile] = useState({
    firstName: user?.firstName || 'Test',
    lastName: user?.lastName || 'User',
    email: user?.email || '<EMAIL>',
    phone: '',
    language: 'es',
    timezone: 'Europe/Madrid',
    notifications: {
      email: true,
      browser: true,
      invoices: true,
      payments: true
    }
  });

  const handleSave = async () => {
    setIsLoading(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsLoading(false);
    // Show success message
  };

  return (
    <DashboardLayout title="Configuración de Perfil">
      <div className="space-y-6">
        {/* Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Información Personal
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nombre
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={profile.firstName}
                  onChange={(e) => setProfile(prev => ({ ...prev, firstName: e.target.value }))}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Apellidos
                </label>
                <input
                  type="text"
                  className="input w-full"
                  value={profile.lastName}
                  onChange={(e) => setProfile(prev => ({ ...prev, lastName: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  className="input w-full"
                  value={profile.email}
                  onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Teléfono
                </label>
                <input
                  type="tel"
                  className="input w-full"
                  value={profile.phone}
                  onChange={(e) => setProfile(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+34 123 456 789"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Preferences */}
        <Card>
          <CardHeader>
            <CardTitle>Preferencias</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Idioma
                </label>
                <select
                  className="input w-full bg-white dark:bg-slate-800 border-gray-300 dark:border-slate-600 text-gray-900 dark:text-white"
                  value={profile.language}
                  onChange={(e) => setProfile(prev => ({ ...prev, language: e.target.value }))}
                >
                  <option value="es">Español</option>
                  <option value="en">English</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Zona Horaria
                </label>
                <select
                  className="input w-full bg-white dark:bg-slate-800 border-gray-300 dark:border-slate-600 text-gray-900 dark:text-white"
                  value={profile.timezone}
                  onChange={(e) => setProfile(prev => ({ ...prev, timezone: e.target.value }))}
                >
                  <option value="Europe/Madrid">Madrid (GMT+1)</option>
                  <option value="Europe/London">Londres (GMT+0)</option>
                  <option value="America/New_York">Nueva York (GMT-5)</option>
                </select>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                  Tema de la Interfaz
                </label>
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => setTheme('light')}
                    className={cn(
                      'flex items-center px-4 py-3 rounded-xl border-2 transition-all duration-300 hover:scale-105',
                      theme === 'light'
                        ? 'border-emerald-500 bg-emerald-50 text-emerald-700 shadow-lg'
                        : 'border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:border-emerald-300 dark:hover:border-emerald-600'
                    )}
                  >
                    <Sun className="h-5 w-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">Modo Claro</div>
                      <div className="text-sm opacity-75">Interfaz clara y brillante</div>
                    </div>
                  </button>

                  <button
                    onClick={() => setTheme('dark')}
                    className={cn(
                      'flex items-center px-4 py-3 rounded-xl border-2 transition-all duration-300 hover:scale-105',
                      theme === 'dark'
                        ? 'border-emerald-500 bg-emerald-50 text-emerald-700 shadow-lg'
                        : 'border-gray-300 dark:border-slate-600 bg-white dark:bg-slate-800 text-gray-700 dark:text-gray-300 hover:border-emerald-300 dark:hover:border-emerald-600'
                    )}
                  >
                    <Moon className="h-5 w-5 mr-3" />
                    <div className="text-left">
                      <div className="font-medium">Modo Oscuro</div>
                      <div className="text-sm opacity-75">Interfaz oscura y elegante</div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bell className="h-5 w-5 mr-2" />
              Notificaciones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Notificaciones por Email</h4>
                  <p className="text-sm text-gray-600">Recibe notificaciones importantes por email</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 rounded"
                  checked={profile.notifications.email}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    notifications: { ...prev.notifications, email: e.target.checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Notificaciones del Navegador</h4>
                  <p className="text-sm text-gray-600">Recibe notificaciones push en el navegador</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 rounded"
                  checked={profile.notifications.browser}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    notifications: { ...prev.notifications, browser: e.target.checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Nuevas Facturas</h4>
                  <p className="text-sm text-gray-600">Notificar cuando se creen nuevas facturas</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 rounded"
                  checked={profile.notifications.invoices}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    notifications: { ...prev.notifications, invoices: e.target.checked }
                  }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Pagos Recibidos</h4>
                  <p className="text-sm text-gray-600">Notificar cuando se reciban pagos</p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 rounded"
                  checked={profile.notifications.payments}
                  onChange={(e) => setProfile(prev => ({
                    ...prev,
                    notifications: { ...prev.notifications, payments: e.target.checked }
                  }))}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Seguridad
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Cambiar Contraseña</h4>
                  <p className="text-sm text-gray-600">Actualiza tu contraseña regularmente</p>
                </div>
                <Button variant="outline">
                  <Lock className="h-4 w-4 mr-2" />
                  Cambiar
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Autenticación de Dos Factores</h4>
                  <p className="text-sm text-gray-600">Añade una capa extra de seguridad</p>
                </div>
                <Button variant="outline">
                  Configurar
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">Sesiones Activas</h4>
                  <p className="text-sm text-gray-600">Gestiona tus sesiones activas</p>
                </div>
                <Button variant="outline">
                  Ver Sesiones
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button 
            onClick={handleSave}
            loading={isLoading}
            className="flex items-center"
          >
            <Save className="h-4 w-4 mr-2" />
            Guardar Cambios
          </Button>
        </div>
      </div>
    </DashboardLayout>
  );
}
