@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 15 23 42; /* Dark background like reference */
    --foreground: 248 250 252; /* Light text */
    --card: 30 41 59; /* Dark card background */
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;
    --primary: 16 185 129; /* Emerald primary */
    --primary-foreground: 15 23 42;
    --secondary: 51 65 85; /* Darker secondary */
    --secondary-foreground: 248 250 252;
    --muted: 51 65 85; /* Muted dark */
    --muted-foreground: 148 163 184; /* Muted text */
    --accent: 51 65 85;
    --accent-foreground: 248 250 252;
    --destructive: 239 68 68; /* Red for destructive actions */
    --destructive-foreground: 248 250 252;
    --border: 51 65 85; /* Dark border */
    --input: 51 65 85;
    --ring: 16 185 129;
    --radius: 1rem; /* Rounded like reference */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(203 213 225) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgb(203 213 225);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgb(148 163 184);
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-r-transparent rounded-full animate-spin;
  }

  /* Card styles - Light and Dark theme support */
  .card {
    @apply bg-white dark:bg-slate-800 text-gray-900 dark:text-slate-100 rounded-2xl border border-gray-200 dark:border-slate-700 shadow-lg hover:shadow-xl transition-all duration-300;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6 pb-4;
  }

  .card-title {
    @apply text-lg font-semibold leading-none tracking-tight text-gray-900 dark:text-slate-100;
  }

  .card-description {
    @apply text-sm text-gray-600 dark:text-slate-400;
  }

  .card-content {
    @apply p-6 pt-2;
  }

  .card-footer {
    @apply flex items-center p-6 pt-4;
  }

  /* Custom gradient cards like reference */
  .gradient-card-green {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  }

  .gradient-card-blue {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }

  .gradient-card-purple {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  }

  .gradient-card-orange {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }

  /* AI Action card gradient */
  .ai-action-card {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    position: relative;
    overflow: hidden;
  }

  .ai-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(30px, -30px);
  }

  /* Status indicators */
  .status-low {
    @apply text-emerald-400 bg-emerald-400/10 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-med {
    @apply text-orange-400 bg-orange-400/10 px-2 py-1 rounded-full text-xs font-medium;
  }

  .status-high {
    @apply text-red-400 bg-red-400/10 px-2 py-1 rounded-full text-xs font-medium;
  }

  /* Button styles - Minimal design */
  .btn {
    @apply inline-flex items-center justify-center rounded-xl text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .btn-primary {
    @apply bg-slate-900 text-white hover:bg-slate-800 shadow-sm;
  }

  .btn-secondary {
    @apply bg-slate-100 text-slate-900 hover:bg-slate-200 border border-slate-200;
  }

  .btn-outline {
    @apply border border-slate-300 bg-transparent text-slate-700 hover:bg-slate-50 hover:text-slate-900;
  }

  .btn-ghost {
    @apply text-slate-700 hover:bg-slate-100 hover:text-slate-900;
  }

  .btn-destructive {
    @apply bg-red-500 text-white hover:bg-red-600 shadow-sm;
  }

  .btn-sm {
    @apply h-8 px-3 text-xs;
  }

  .btn-md {
    @apply h-10 px-4 py-2;
  }

  .btn-lg {
    @apply h-12 px-8 text-base;
  }

  /* Input styles - Minimal design */
  .input {
    @apply flex h-10 w-full rounded-xl border border-slate-300 bg-white px-3 py-2 text-sm placeholder:text-slate-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-slate-400 focus-visible:ring-offset-2 focus-visible:border-slate-400 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-200;
  }

  /* Table styles - Minimal design */
  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b border-slate-200;
  }

  .table-row {
    @apply border-b border-slate-100 transition-colors hover:bg-slate-50/50;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-slate-500 bg-slate-50/50;
  }

  .table-cell {
    @apply p-4 align-middle text-slate-700;
  }

  /* Badge styles - Minimal design */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-colors;
  }

  .badge-default {
    @apply bg-slate-100 text-slate-800;
  }

  .badge-secondary {
    @apply bg-slate-50 text-slate-600 border border-slate-200;
  }

  .badge-destructive {
    @apply bg-red-100 text-red-800;
  }

  .badge-outline {
    @apply border border-slate-200 text-slate-700 bg-white;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideIn {
    from {
      transform: translateY(-10px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
}
